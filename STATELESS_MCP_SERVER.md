# Stateless MCP Server Configuration

## Overview

The MCP server is now configured to run in **stateless mode** using StreamableHTTP transport. This means every request is treated as a new session with no state maintained between requests.

## Key Configuration

```go
httpServer = server.NewStreamableHTTPServer(
    mcpServer,
    server.WithStateLess(true),                    // Enable stateless mode
    server.WithEndpointPath("/mcp"),               // Endpoint path
    server.WithHeartbeatInterval(0),               // Disable heartbeat
)
```

## Benefits of Stateless Mode

1. **Maximum Simplicity**: No session management complexity
2. **Zero Memory Overhead**: No session storage or cleanup required
3. **Perfect Scalability**: Each request is completely independent
4. **No Resource Leaks**: No sessions to expire or clean up
5. **Stateless Architecture**: Follows REST principles

## How It Works

### Request Flow
1. Client sends HTTP request to `/mcp` endpoint
2. Server processes request as a new session
3. Server responds with result
4. No state is maintained after response

### Session Handling
- Each request gets a temporary session ID (if needed)
- Session ID is not persisted or reused
- No session expiration or cleanup needed
- Tools still receive SessionID and RequestID parameters as before

## Comparison: Stateful vs Stateless

| Aspect | Stateful (with SessionManager) | Stateless (current) |
|--------|--------------------------------|---------------------|
| Memory Usage | Grows with active sessions | Constant, minimal |
| Scalability | Limited by session storage | Unlimited |
| Complexity | Session lifecycle management | Simple request/response |
| Resource Cleanup | Required (TTL, cleanup) | Not needed |
| State Between Requests | Maintained | None |
| Performance | Overhead for session ops | Minimal overhead |

## Tool Parameters

The existing tool parameter injection remains unchanged:
- `SessionID`: Still provided to tools (temporary per request)
- `RequestID`: Still provided to tools (unique per request)

## Monitoring

Use `GetSessionStats()` to check server status:

```go
stats := server.GetSessionStats()
// Returns:
// {
//   "active_sessions": "N/A (stateless mode)",
//   "server_type": "StreamableHTTP",
//   "stateless": true,
//   "persistent_connections": false,
//   "session_management": "Each request is treated as a new session"
// }
```

## Testing

All tests pass with stateless configuration:
```bash
cd src && go test ./usecase/aica -v
```

## When to Use Stateless vs Stateful

### Use Stateless When:
- ✅ You want maximum simplicity
- ✅ You don't need state between requests
- ✅ You want perfect horizontal scalability
- ✅ You prefer REST-like architecture
- ✅ You want zero memory overhead

### Use Stateful When:
- ❌ You need to maintain state between requests
- ❌ You have complex session-based workflows
- ❌ You need session-specific tool configurations
- ❌ You want to track user sessions over time

## Current Status

✅ **Stateless mode is now active**
- No session manager required
- No session storage overhead
- Each request is independent
- Perfect for REST-like MCP interactions
