{"name": "@modelcontextprotocol/inspector", "version": "0.17.0", "description": "Model Context Protocol inspector", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/inspector/issues", "type": "module", "bin": {"mcp-inspector": "cli/build/cli.js"}, "files": ["client/bin", "client/dist", "server/build", "cli/build"], "workspaces": ["client", "server", "cli"], "scripts": {"build": "npm run build-server && npm run build-client && npm run build-cli", "build-server": "cd server && npm run build", "build-client": "cd client && npm run build", "build-cli": "cd cli && npm run build", "clean": "rimraf ./node_modules ./client/node_modules ./cli/node_modules ./build ./client/dist ./server/build ./cli/build ./package-lock.json && npm install", "dev": "node client/bin/start.js --dev", "dev:windows": "node client/bin/start.js --dev", "dev:sdk": "npm run link:sdk && concurrently \"npm run dev\" \"cd sdk && npm run build:esm:w\"", "link:sdk": "(test -d sdk || ln -sf ${MCP_SDK:-$PWD/../typescript-sdk} sdk) && (cd sdk && npm link && (test -d node_modules || npm i)) && npm link @modelcontextprotocol/sdk", "unlink:sdk": "(cd sdk && npm unlink -g) && rm sdk && npm unlink @modelcontextprotocol/sdk", "start": "node client/bin/start.js", "start-server": "cd server && npm run start", "start-client": "cd client && npm run preview", "test": "npm run prettier-check && cd client && npm test", "test-cli": "cd cli && npm run test", "test:e2e": "MCP_AUTO_OPEN_ENABLED=false npm run test:e2e --workspace=client", "prettier-fix": "prettier --write .", "prettier-check": "prettier --check .", "lint": "prettier --check . && cd client && npm run lint", "prepare": "husky && npm run build", "publish-all": "npm publish --workspaces --access public && npm publish --access public", "update-version": "node scripts/update-version.js", "check-version": "node scripts/check-version-consistency.js"}, "dependencies": {"@modelcontextprotocol/inspector-cli": "^0.17.0", "@modelcontextprotocol/inspector-client": "^0.17.0", "@modelcontextprotocol/inspector-server": "^0.17.0", "@modelcontextprotocol/sdk": "^1.18.0", "concurrently": "^9.2.0", "node-fetch": "^3.3.2", "open": "^10.2.0", "shell-quote": "^1.8.3", "spawn-rx": "^5.1.2", "ts-node": "^10.9.2", "zod": "^3.25.76"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/jest": "^29.5.14", "@types/node": "^22.17.0", "@types/shell-quote": "^1.7.5", "husky": "^9.1.7", "jest-fixed-jsdom": "^0.0.9", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "rimraf": "^6.0.1", "typescript": "^5.4.2"}, "engines": {"node": ">=22.7.5"}, "lint-staged": {"**/*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}}