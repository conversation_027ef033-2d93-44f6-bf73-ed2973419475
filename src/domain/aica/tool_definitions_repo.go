package aica

import "gorm.io/gorm"

type (
	ToolDefinitionsRepository struct {
		db *gorm.DB
	}
)

func NewToolDefinitionsRepository(db *gorm.DB) *ToolDefinitionsRepository {
	return &ToolDefinitionsRepository{db: db}
}

func (r *ToolDefinitionsRepository) AllAvailables() ([]*ToolDefinitions, error) {
	var tools []*ToolDefinitions
	if err := r.db.Find(&tools).Error; err != nil {
		return nil, err
	}
	return tools, nil
}
