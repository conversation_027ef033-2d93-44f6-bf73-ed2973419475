package tools

import (
	"aica/mcp/sdk/debug"
	mlogger "aica/mcp/sdk/logger"
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

type (
	toolSearchOccupationsByWorkNature struct {
		name string
	}
)

func newToolSearchOccupationsByWorkNature() *toolSearchOccupationsByWorkNature {
	return &toolSearchOccupationsByWorkNature{name: "search_occupations_by_work_nature"}
}

func (t toolSearchOccupationsByWorkNature) getName() string {
	return t.name
}

func (t toolSearchOccupationsByWorkNature) createToolHandler(apiServer string, getProvider func() string) server.ToolHandlerFunc {

	// create TypedHandlerFunc
	handler := func(ctx context.Context, request mcp.CallToolRequest, args searchOccupationsByNatureRequest) (*mcp.CallToolResult, error) {
		debug.Log(t.name, "args", args)

		_, errorToolResult := initializeTool(t.name, &args.commonRequest)
		if errorToolResult != nil {
			return errorToolResult, nil
		}

		if len(args.JobNaturePreferences) == 0 {
			return NewErrorCallToolResultInvalidArgument("JobNaturePreferences"), nil
		}

		logger := mlogger.GetMCPContext().NewToolLogger("SessionID", args.SessionID)

		natureScore := float64(minNatureScore)
		jobTypeScore := float64(minJobTypeScore)
		payload := jobTypeSearchAPIRequest{
			JobNaturePreferences: args.JobNaturePreferences,
			MinNatureScore:       &natureScore,
			MinJobTypeScore:      &jobTypeScore,
		}

		url := fmt.Sprintf("%s/aica/mcptool/jobtype/search/nature", apiServer)
		debug.Log(t.name, "url", url)

		client := http.Client{}
		req, err := newPostRequest(args.commonRequest, url, payload)
		if err != nil {
			logger.Error("newPostRequestが失敗しました。", "tool", t.name, "url", url, "payload", payload, "error", err)
			return nil, err
		}

		resp, err := client.Do(req)
		if err != nil {
			logger.Error("APIリクエストが失敗しました。", "tool", t.name, "url", url, "payload", payload, "error", err)
			return nil, err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			logger.Error("APIリクエストが失敗しました。", "tool", t.name, "url", url, "payload", payload, "status_code", resp.StatusCode)
			return nil, fmt.Errorf("想定外APIレスポンスステータスコード: %d", resp.StatusCode)
		}

		var data []jobTypeSearchAPIResult
		if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
			logger.Error("レスポンス解析が失敗しました。", "tool", t.name, "url", url, "payload", payload, "error", err)
			return nil, err
		}

		debug.Log(t.name, "Response data", data)

		if len(data) == 0 {
			return NewErrorCallToolResultMessageToLLM("該当する職種が見つかりませんでした。"), nil
		}

		jobTypeSmallIDs := make([]int, len(data))
		for i, item := range data {
			jobTypeSmallIDs[i] = item.ID
		}

		jobTypeResults := make([]map[string]string, len(data))
		for i, item := range data {
			jobTypeResults[i] = map[string]string{
				"職種名":  item.Name,
				"職種説明": item.Description,
			}
		}

		content, err := json.Marshal(map[string]any{
			"職種": jobTypeResults,
		})
		if err != nil {
			logger.Error("ツール結果生成が失敗しました。", "tool", t.name, "url", url, "payload", payload, "jobTypeResults", jobTypeResults, "error", err)
			return nil, err
		}

		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.NewTextContent(string(content)),
			},
		}, nil

	}

	// return server.ToolHandlerFunc
	return mcp.NewTypedToolHandler(handler)
}

func init() {
	tool := newToolSearchOccupationsByWorkNature()
	addToolHandler(tool)
}
