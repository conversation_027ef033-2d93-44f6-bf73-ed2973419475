package tools

type (
	// 共通のリクエストパラメーター
	commonRequest struct {
		SessionID string
		RequestID string
	}

	// 希望職種保存リクエストパラメーター
	SaveOccupationPreferenceRequest struct {
		commonRequest
		OccupationNames []string
	}

	// 業種の意味情報検索のリクエスト
	searchIndustriesBySentenceRequest struct {
		commonRequest
		Sentence string
	}

	// 職種の性質からの検索のリクエスト
	searchOccupationsByNatureRequest struct {
		commonRequest
		JobNaturePreferences []*jobNaturePreference
	}

	// APIの職種検索のリクエスト
	jobTypeSearchAPIRequest struct {
		JobNaturePreferences       []*jobNaturePreference
		MinNatureScore             *float64
		MinJobTypeScore            *float64
		MaxPriorExperienceRequired *float64
	}

	// APIの職種検索結果
	jobTypeSearchAPIResult struct {
		ID          int
		Name        string
		Description string
	}

	// 職種の意味情報検索のリクエスト
	searchOccupationsBySentenceRequest struct {
		commonRequest
		Sentence string
	}

	vectorSearchParams struct {
		Provider string
		Keyword  string
		Distance float64
		Limit    int
	}

	jobNaturePreference struct {
		JobNature  string
		Preference string
	}

	// 場所のリクエスト
	locationRequest struct {
		LocationType   string
		PrefectureName string
		CityName       string
	}

	applicationParams struct {
		commonRequest
		ApplicationType string //面接かカジュアル面談か
		// 応募先のポジションID
		PositionID   int
		Registration bool
	}
	registrationParams struct {
		commonRequest
		Registration bool
	}
	// ポジション検索パラメータ
	positionSearchParams struct {
		commonRequest
		// 確約年収（下限）
		Salary int
		// 希望勤務市区町村ID 場所の1つの項目
		Locations []*locationRequest
		// フルリモート 場所の1つの項目
		FullyRemoteWork bool
		// ポジションのキーワード 仕事内容の1つの項目
		PositionKeyword string
		// 希望職種ID 仕事内容の1つの項目
		JobTypeNames []string
		// 業種センテンス
		IndustrySentence string
		// 休日
		DayOffs []string
		// 平均残業時間
		AverageOvertime []string
		// 最大件数
		Limit int
	}

	industry struct {
		IndustrySmallID   int
		IndustrySmallName string
		Description       string
		Distance          float64
	}

	saveUserPreferencesRequest struct {
		commonRequest
		Amount                       int      `json:"Amount,omitempty"`
		Scope                        string   `json:"Scope,omitempty"`
		JobtypeNames                 []string `json:"JobtypeNames,omitempty"`
		LocationPrefectureResidence  string   `json:"Location_Prefecture_Residence,omitempty"`
		LocationCityResidence        string   `json:"Location_City_Residence,omitempty"`
		LocationPrefecturePreference string   `json:"Location_Prefecture_Preference,omitempty"`
		LocationCityPreference       string   `json:"Location_City_Preference,omitempty"`
		IndustrySentence             string   `json:"Industry_Sentence,omitempty"`
		AverageOvertime              string   `json:"AverageOvertime,omitempty"`
		DayOffs                      string   `json:"DayOffs,omitempty"`
		FullyRemoteWork              bool     `json:"FullyRemoteWork,omitempty"`
		UserPreferencesInSentence    string   `json:"UserPreferences_In_Sentence,omitempty"`
	}
)
