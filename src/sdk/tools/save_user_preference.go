package tools

import (
	"aica/mcp/sdk/debug"
	"context"
	"encoding/json"
	"slices"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

type toolSaveUserPreferences struct {
	name string
}

func newToolSaveUserPreferences() *toolSaveUserPreferences {
	return &toolSaveUserPreferences{name: "save_user_preference"}
}

func (t toolSaveUserPreferences) getName() string {
	return t.name
}

func (t toolSaveUserPreferences) createToolHandler(apiServer string, getProvider func() string) server.ToolHandlerFunc {

	// create TypedHandlerFunc
	handler := func(ctx context.Context, request mcp.CallToolRequest, args saveUserPreferencesRequest) (*mcp.CallToolResult, error) {
		debug.Log(t.name, "args", args)

		_, errorToolResult := initializeTool(t.name, &args.commonRequest)
		if errorToolResult != nil {
			return errorToolResult, nil
		}

		// ポジション検索の際に給料が必須ですが、
		// このツールが呼び出されるときに、常にすべてのフィールドに値が入っているわけではないので、
		// ここでは0は許されます。
		if args.Amount <= -1 {
			return NewErrorCallToolResultInvalidArgument("Amount"), nil
		}

		// Validate Scope if provided
		if args.Scope != "" {
			validScopes := []string{"Minimum"}
			if !slices.Contains(validScopes, args.Scope) {
				return NewErrorCallToolResultInvalidArgument("Scope"), nil
			}
		}

		// Validate DayOff_Preference if provided
		if args.DayOffs != "" {
			validValues := map[string]bool{
				"土日祝休み":  true,
				"毎週2日休み": true,
				"その他":    true,
			}
			if !validValues[args.DayOffs] {
				return NewErrorCallToolResultInvalidArgument("DayOff_Preference"), nil
			}
		}

		// Validate AverageOverTime_Preference if provided
		if args.AverageOvertime != "" && args.AverageOvertime != "10時間以内" {
			return NewErrorCallToolResultInvalidArgument("AverageOverTime_Preference"), nil
		}

		// Build result map
		resultMap := map[string]any{}

		resultMap["Salary"] = args.Amount

		if len(args.JobtypeNames) > 0 {
			resultMap["JobtypeNames"] = args.JobtypeNames
		}

		locations := []map[string]string{}
		if args.LocationPrefectureResidence != "" && args.LocationCityResidence != "" {
			locations = append(locations, map[string]string{
				"LocationType":   "居住地",
				"PrefectureName": args.LocationPrefectureResidence,
				"CityName":       args.LocationCityResidence,
			})
		}
		if args.LocationPrefecturePreference != "" && args.LocationCityPreference != "" {
			locations = append(locations, map[string]string{
				"LocationType":   "希望勤務地",
				"PrefectureName": args.LocationPrefecturePreference,
				"CityName":       args.LocationCityPreference,
			})
		}
		if len(locations) > 0 {
			resultMap["Locations"] = locations
		}

		if args.IndustrySentence != "" {
			resultMap["IndustrySentence"] = args.IndustrySentence
		}

		if args.DayOffs != "" {
			resultMap["DayOffs"] = args.DayOffs
		}

		if args.AverageOvertime != "" {
			resultMap["AverageOvertime"] = args.AverageOvertime
		}

		resultMap["FullyRemoteWork"] = args.FullyRemoteWork

		resultMap[messageToLLMKey] = "情報を保存しました。"

		content, err := json.Marshal(resultMap)
		if err != nil {
			return NewErrorCallToolResultMessageToLLM("情報を保存できませんでした。"), nil
		}

		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.NewTextContent(string(content)),
			},
		}, nil
	}

	// return server.ToolHandlerFunc
	return mcp.NewTypedToolHandler(handler)
}

func init() {
	tool := newToolSaveUserPreferences()
	addToolHandler(tool)
}
