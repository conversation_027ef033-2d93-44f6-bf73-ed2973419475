package tools

import (
	"aica/mcp/sdk/debug"
	"context"
	"encoding/json"
	"slices"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

type toolSaveUserPersonalInfo struct {
	name string
}

// Request structure matching the JSON schema
type saveUserPersonalInfoRequest struct {
	commonRequest
	UserName                  *string  `json:"User_Name,omitempty"`
	InterestTendency          *string  `json:"Interest_Tendency,omitempty"`
	JobSearchMotivation       *string  `json:"Job_Search_Motivation,omitempty"`
	CurrentJobExperienceYears *float64 `json:"Current_Job_Experience_Years,omitempty"`
	CurrentJobDescription     *string  `json:"Current_Job_Description,omitempty"`
	JobFeedbackPositive       []string `json:"Job_Feedback_Positive,omitempty"`
	JobFeedbackNegative       []string `json:"Job_Feedback_Negative,omitempty"`
	UserPurpose               *string  `json:"UserPurpose,omitempty"`
	JobSearchFilter           *string  `json:"JobSearchFilter,omitempty"`
	ApplicationType           *string  `json:"ApplicationType,omitempty"`
	Registration              *bool    `json:"Registration,omitempty"`
	PositionID                *int     `json:"PositionID,omitempty"`
}

func newToolSaveUserPersonalInfo() *toolSaveUserPersonalInfo {
	return &toolSaveUserPersonalInfo{name: "save_user_personal_info"}
}

func (t toolSaveUserPersonalInfo) getName() string {
	return t.name
}

func (t toolSaveUserPersonalInfo) createToolHandler(apiServer string, getProvider func() string) server.ToolHandlerFunc {

	// create TypedHandlerFunc
	handler := func(ctx context.Context, request mcp.CallToolRequest, args saveUserPersonalInfoRequest) (*mcp.CallToolResult, error) {
		debug.Log(t.name, "args", args)

		_, errorToolResult := initializeTool(t.name, &args.commonRequest)
		if errorToolResult != nil {
			return errorToolResult, nil
		}

		// Validate experience years if provided
		if args.CurrentJobExperienceYears != nil {
			if *args.CurrentJobExperienceYears < 0 {
				return NewErrorCallToolResultInvalidArgument("Current_Job_Experience_Years"), nil
			}
		}

		// Validate ApplicationType if provided
		if args.ApplicationType != nil {
			validTypes := []string{"面接選考", "カジュアル面談", ""}
			isValid := slices.Contains(validTypes, *args.ApplicationType)
			if !isValid {
				return NewErrorCallToolResultInvalidArgument("ApplicationType"), nil
			}
		}

		// Validate PositionID if provided
		if args.PositionID != nil {
			if *args.PositionID < 0 {
				return NewErrorCallToolResultInvalidArgument("PositionID"), nil
			}
		}

		// Build resultMap
		resultMap := map[string]any{}

		if args.UserName != nil && *args.UserName != "" {
			resultMap["UserName"] = *args.UserName
		}

		if args.InterestTendency != nil && *args.InterestTendency != "" {
			resultMap["InterestTendency"] = *args.InterestTendency
		}

		if args.JobSearchMotivation != nil && *args.JobSearchMotivation != "" {
			resultMap["JobSearchMotivation"] = *args.JobSearchMotivation
		}

		if args.CurrentJobExperienceYears != nil {
			resultMap["CurrentJobExperienceYears"] = *args.CurrentJobExperienceYears
		}

		if args.CurrentJobDescription != nil && *args.CurrentJobDescription != "" {
			resultMap["CurrentJobDescription"] = *args.CurrentJobDescription
		}

		if len(args.JobFeedbackPositive) > 0 {
			resultMap["JobFeedbackPositive"] = args.JobFeedbackPositive
		}

		if len(args.JobFeedbackNegative) > 0 {
			resultMap["JobFeedbackNegative"] = args.JobFeedbackNegative
		}

		if args.UserPurpose != nil && *args.UserPurpose != "" {
			resultMap["UserPurpose"] = *args.UserPurpose
		}

		if args.JobSearchFilter != nil && *args.JobSearchFilter != "" {
			resultMap["JobSearchFilter"] = *args.JobSearchFilter
		}

		if args.ApplicationType != nil {
			resultMap["ApplicationType"] = *args.ApplicationType
		}

		if args.Registration != nil {
			resultMap["Registration"] = *args.Registration
		}

		if args.PositionID != nil {
			resultMap["PositionID"] = *args.PositionID
		}

		resultMap["MessageToLLM"] = "情報を保存しました。"

		content, err := json.Marshal(resultMap)
		if err != nil {
			return NewErrorCallToolResultMessageToLLM("情報を保存できませんでした。"), nil
		}

		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.NewTextContent(string(content)),
			},
		}, nil
	}

	// return server.ToolHandlerFunc
	return mcp.NewTypedToolHandler(handler)
}

func init() {
	tool := newToolSaveUserPersonalInfo()
	addToolHandler(tool)
}
