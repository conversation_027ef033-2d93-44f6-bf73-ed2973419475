package tools

import (
	mlogger "aica/mcp/sdk/logger"
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/google/uuid"
	"github.com/mark3labs/mcp-go/mcp"
)

const (
	InternalToolNamePrefix = "internal_tool_"
	distance               = 1
	limit                  = 10
	minNatureScore         = 3
	minJobTypeScore        = 0.5
	messageToLLMKey        = "MessageToLLM"
)

func NewDefaultCallToolResult() *mcp.CallToolResult {
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.NewTextContent("{\"ツール実行結果\": \"成功。\"}"),
		},
		IsError: false,
	}
}

func NewErrorCallToolResultMissingArgument(argumentName string) *mcp.CallToolResult {
	return mcp.NewToolResultError(fmt.Sprintf("{\"Message\": \"引数%sは必要なので、ユーザーに聞いて下さい。\"}", argumentName))
}

func NewErrorCallToolResultInvalidArgument(argumentName string) *mcp.CallToolResult {
	return mcp.NewToolResultError(fmt.Sprintf("{\"Message\": \"引数%sの値は無効なので、ユーザーに確認してください。\"}", argumentName))
}

func NewErrorCallToolResultInvalidSessionID() *mcp.CallToolResult {
	return mcp.NewToolResultError("{\"Message\": \"引数SessionIDの値は無効なので、一番最初のメッセージに入っているので、そこから取ってください。\"}")
}

func NewErrorCallToolResultMessageToLLM(message string) *mcp.CallToolResult {
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.NewTextContent(fmt.Sprintf("{\"%s\": \"%s\"}", messageToLLMKey, message)),
		},
	}
}

func newPostRequest(traceInfo commonRequest, url string, payload any) (*http.Request, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	req.Header = http.Header{
		"X-SESSION-ID": {traceInfo.SessionID},
		"X-REQUEST-ID": {traceInfo.RequestID},
		"Content-Type": {"application/json"},
	}

	return req, nil
}

func initializeTool(toolName string, traceInfo *commonRequest) (mlogger.LevelLogger, *mcp.CallToolResult) {
	if len(traceInfo.SessionID) == 0 {
		return nil, NewErrorCallToolResultInvalidSessionID()
	}

	if len(traceInfo.RequestID) == 0 {
		traceInfo.RequestID = uuid.NewString()
	}

	logger := mlogger.GetMCPContext().NewToolLogger("SessionID", traceInfo.SessionID, "RequestID", traceInfo.RequestID)
	logger.Info("トレーシング", "tool", toolName, "SessionID", traceInfo.SessionID, "RequestID", traceInfo.RequestID)
	return logger, nil
}
