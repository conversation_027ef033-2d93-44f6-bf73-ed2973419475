package tools

import (
	"aica/mcp/sdk/debug"
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

type toolSearchJobPosting struct {
	name string
}

func newToolSearchJobPosting() *toolSearchJobPosting {
	// ミイダス社内では求人のことをPositionと言うが
	// MCPでは求人の英語表記をJob Postingで統一
	return &toolSearchJobPosting{name: "search_job_postings"}
}

func (t toolSearchJobPosting) getName() string {
	return t.name
}

func (t toolSearchJobPosting) createToolHandler(apiServer string, getProvider func() string) server.ToolHandlerFunc {
	// create TypedHandlerFunc
	handler := func(ctx context.Context,
		request mcp.CallToolRequest,
		args positionSearchParams) (*mcp.CallToolResult, error,
	) {
		debug.Log(t.name, "args", args)

		logger, errorToolResult := initializeTool(t.name, &args.commonRequest)
		if errorToolResult != nil {
			return errorToolResult, nil
		}

		// Limit
		if args.Limit <= 0 {
			args.Limit = limit
		}

		url := fmt.Sprintf("%s/aica/mcptool/positions/search", apiServer)
		debug.Log(t.name, "url", url)

		client := http.Client{}
		req, err := newPostRequest(args.commonRequest, url, args)
		if err != nil {
			logger.Error("newPostRequestが失敗しました。", "tool", t.name, "url", url, "payload", args, "error", err)
			return nil, err
		}

		resp, err := client.Do(req)
		if err != nil {
			logger.Error("APIリクエストが失敗しました。", "tool", t.name, "url", url, "payload", args, "error", err)
			return nil, err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusBadRequest {
			logger.Error("APIリクエストが失敗しました。", "tool", t.name, "url", url, "payload", args, "status", resp.Status)
			return nil, fmt.Errorf("APIリクエストが失敗しました: %s", resp.Status)
		}

		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			logger.Error("レスポンス解析が失敗しました。", "tool", t.name, "url", url, "payload", args, "data", bodyBytes, "error", err)
			return nil, err
		}
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.NewTextContent(string(bodyBytes)),
			},
			IsError: resp.StatusCode == http.StatusBadRequest,
		}, nil
	}

	// return server.ToolHandlerFunc
	return mcp.NewTypedToolHandler(handler)
}

func init() {
	tool := newToolSearchJobPosting()
	addToolHandler(tool)
}
