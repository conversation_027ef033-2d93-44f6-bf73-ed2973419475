package aica

import (
	"context"
	"os"
	"testing"
	"time"

	mlogger "aica/mcp/sdk/logger"

	"gorm.io/gorm"
)

func TestSessionManager(t *testing.T) {
	// Test session manager functionality
	sm := NewSessionManager(5 * time.Minute)

	// Test session generation
	sessionID := sm.Generate()
	if sessionID == "" {
		t.<PERSON>rror("Expected non-empty session ID")
	}

	// Test session validation
	isTerminated, err := sm.Validate(sessionID)
	if err != nil {
		t.Errorf("Unexpected error validating session: %v", err)
	}
	if isTerminated {
		t.Error("Expected session to not be terminated")
	}

	// Test session termination
	isNotAllowed, err := sm.Terminate(sessionID)
	if err != nil {
		t.<PERSON>rrorf("Unexpected error terminating session: %v", err)
	}
	if isNotAllowed {
		t.Error("Expected termination to be allowed")
	}

	// Test session count
	count := sm.GetSessionCount()
	if count != 0 {
		t.<PERSON>("Expected 0 sessions after termination, got %d", count)
	}
}

func TestServerUsecase_GetSessionStats(t *testing.T) {
	// Create a mock server usecase
	server := &ServerUsecase{
		db:        &gorm.DB{}, // Mock DB
		port:      "8080",
		apiServer: "http://localhost:10001",
	}

	// Test getting session stats in stateless mode
	stats := server.GetSessionStats()

	// Verify stats structure for stateless mode
	if stats["server_type"] != "StreamableHTTP" {
		t.Errorf("Expected server_type to be 'StreamableHTTP', got %v", stats["server_type"])
	}

	if stats["persistent_connections"] != false {
		t.Errorf("Expected persistent_connections to be false, got %v", stats["persistent_connections"])
	}

	if stats["stateless"] != true {
		t.Errorf("Expected stateless to be true, got %v", stats["stateless"])
	}

	if _, exists := stats["active_sessions"]; !exists {
		t.Error("Expected active_sessions to be present in stats")
	}

	if _, exists := stats["session_management"]; !exists {
		t.Error("Expected session_management to be present in stats")
	}
}

func TestServerUsecase_Shutdown(t *testing.T) {
	// Initialize logger context to avoid nil pointer
	mlogger.NewMCPContext("test", mlogger.Writer(os.Stdout))

	server := &ServerUsecase{
		db:        &gorm.DB{}, // Mock DB
		port:      "8080",
		apiServer: "http://localhost:10001",
	}

	// Test shutdown without starting server (should not panic)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := server.Shutdown(ctx)
	if err != nil {
		t.Errorf("Unexpected error during shutdown: %v", err)
	}
}
