package aica

import (
	"context"
	"os"
	"testing"
	"time"

	mlogger "aica/mcp/sdk/logger"

	"gorm.io/gorm"
)

// TestSessionManager removed - not needed for stateless server

func TestServerUsecase_GetSessionStats(t *testing.T) {
	// Create a mock server usecase
	server := &ServerUsecase{
		db:        &gorm.DB{}, // Mock DB
		port:      "8080",
		apiServer: "http://localhost:10001",
	}

	// Test getting session stats in stateless mode
	stats := server.GetSessionStats()

	// Verify stats structure for stateless mode
	if stats["server_type"] != "StreamableHTTP" {
		t.<PERSON>rrorf("Expected server_type to be 'StreamableHTTP', got %v", stats["server_type"])
	}

	if stats["persistent_connections"] != false {
		t.<PERSON>rf("Expected persistent_connections to be false, got %v", stats["persistent_connections"])
	}

	if stats["stateless"] != true {
		t.<PERSON>rrorf("Expected stateless to be true, got %v", stats["stateless"])
	}

	if _, exists := stats["active_sessions"]; !exists {
		t.<PERSON>rror("Expected active_sessions to be present in stats")
	}

	if _, exists := stats["session_management"]; !exists {
		t.Error("Expected session_management to be present in stats")
	}
}

func TestServerUsecase_Shutdown(t *testing.T) {
	// Initialize logger context to avoid nil pointer
	mlogger.NewMCPContext("test", mlogger.Writer(os.Stdout))

	server := &ServerUsecase{
		db:        &gorm.DB{}, // Mock DB
		port:      "8080",
		apiServer: "http://localhost:10001",
	}

	// Test shutdown without starting server (should not panic)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := server.Shutdown(ctx)
	if err != nil {
		t.Errorf("Unexpected error during shutdown: %v", err)
	}
}
