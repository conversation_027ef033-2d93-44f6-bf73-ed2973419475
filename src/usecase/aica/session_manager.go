package aica

import (
	"sync"
	"time"

	"github.com/google/uuid"
)

// SessionManager handles session management for the MCP server
type SessionManager struct {
	sessions map[string]*SessionInfo
	mutex    sync.RWMutex
	ttl      time.Duration
}

// SessionInfo stores information about a session
type SessionInfo struct {
	ID        string
	CreatedAt time.Time
	LastUsed  time.Time
}

// NewSessionManager creates a new session manager
func NewSessionManager(ttl time.Duration) *SessionManager {
	sm := &SessionManager{
		sessions: make(map[string]*SessionInfo),
		ttl:      ttl,
	}
	
	// Start cleanup goroutine
	go sm.cleanup()
	
	return sm
}

// Generate creates a new session ID
func (sm *SessionManager) Generate() string {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sessionID := uuid.New().String()
	now := time.Now()
	
	sm.sessions[sessionID] = &SessionInfo{
		ID:        sessionID,
		CreatedAt: now,
		LastUsed:  now,
	}
	
	return sessionID
}

// Validate checks if a session ID is valid and not terminated
func (sm *SessionManager) Validate(sessionID string) (isTerminated bool, err error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	session, exists := sm.sessions[sessionID]
	if !exists {
		return false, nil // Session doesn't exist, not terminated but invalid
	}
	
	// Check if session has expired
	if time.Since(session.LastUsed) > sm.ttl {
		return true, nil // Session expired, considered terminated
	}
	
	// Update last used time
	session.LastUsed = time.Now()
	
	return false, nil // Session is valid and not terminated
}

// Terminate marks a session ID as terminated
func (sm *SessionManager) Terminate(sessionID string) (isNotAllowed bool, err error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	delete(sm.sessions, sessionID)
	return false, nil // Termination is allowed
}

// cleanup removes expired sessions
func (sm *SessionManager) cleanup() {
	ticker := time.NewTicker(time.Minute * 5) // Cleanup every 5 minutes
	defer ticker.Stop()
	
	for range ticker.C {
		sm.mutex.Lock()
		now := time.Now()
		for id, session := range sm.sessions {
			if now.Sub(session.LastUsed) > sm.ttl {
				delete(sm.sessions, id)
			}
		}
		sm.mutex.Unlock()
	}
}

// GetSessionCount returns the number of active sessions
func (sm *SessionManager) GetSessionCount() int {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return len(sm.sessions)
}
