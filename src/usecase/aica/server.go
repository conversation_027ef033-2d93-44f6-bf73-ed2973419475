package aica

import (
	"aica/mcp/domain/aica"
	mlogger "aica/mcp/sdk/logger"
	"aica/mcp/sdk/tools"
	"context"
	"encoding/json"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	AgentProviderBedrock = "bedrock"
	AgentProviderOpenAI  = "openai"

	serverName    = "aica-server"
	serverVersion = "1.0.0"
)

var (
	mcpServer       *server.MCPServer
	toolDefinitions []*aica.ToolDefinitions
	httpServer      *server.StreamableHTTPServer
)

type (
	ServerUsecase struct {
		db        *gorm.DB
		port      string
		apiServer string
	}
)

var (
	provider string = AgentProviderOpenAI
)

func GetProvider() string {
	return provider
}

func NewServerUsecase(db *gorm.DB, port string, apiServer string) *ServerUsecase {
	return &ServerUsecase{db: db, port: port, apiServer: apiServer}
}

func (u *ServerUsecase) newMCPServer() {
	mcpServer = server.NewMCPServer(
		serverName,
		serverVersion,
		server.WithPromptCapabilities(true),
		server.WithToolCapabilities(true),
		server.WithLogging(),
		server.WithHooks(&server.Hooks{
			OnBeforeListTools: []server.OnBeforeListToolsFunc{
				func(ctx context.Context, id any, message *mcp.ListToolsRequest) {
					u.addTools()
				},
			},
		}),
	)
}

func (u *ServerUsecase) Start() error {
	logger := mlogger.GetMCPContext().NewMCPLogger()

	u.newMCPServer()

	// Create StreamableHTTP server in stateless mode
	// Note: WithStateLess(true) means every request is treated as a new session
	httpServer = server.NewStreamableHTTPServer(
		mcpServer,
		server.WithStateLess(true),
		server.WithEndpointPath("/mcp"),
		server.WithHeartbeatInterval(0), // Disable heartbeat for non-persistent connections
	)

	logger.Info("Starting StreamableHTTP server", "port", u.port, "endpoint", "/mcp")
	if err := httpServer.Start(fmt.Sprintf(":%s", u.port)); err != nil {
		logger.Error("Failed to start StreamableHTTP server", "error", err)
		return err
	}

	return nil
}

func (u *ServerUsecase) Shutdown(ctx context.Context) error {
	logger := mlogger.GetMCPContext().NewMCPLogger()

	if httpServer != nil {
		logger.Info("Shutting down StreamableHTTP server")
		if err := httpServer.Shutdown(ctx); err != nil {
			logger.Error("Failed to shutdown StreamableHTTP server", "error", err)
			return err
		}
	} else {
		logger.Info("No server to shutdown")
	}

	logger.Info("Server shutdown completed")
	return nil
}

func (u *ServerUsecase) GetSessionStats() map[string]any {
	stats := make(map[string]any)

	// In stateless mode, there are no persistent sessions
	stats["active_sessions"] = "N/A (stateless mode)"
	stats["server_type"] = "StreamableHTTP"
	stats["stateless"] = true
	stats["persistent_connections"] = false
	stats["session_management"] = "Each request is treated as a new session"

	return stats
}

func (u *ServerUsecase) addTools() error {
	logger := mlogger.GetMCPContext().NewMCPLogger()

	if toolDefinitions != nil {
		mcpServer.DeleteTools(lo.Map(toolDefinitions, func(toolDefinition *aica.ToolDefinitions, _ int) string {
			return toolDefinition.Name
		})...)
	}

	var err error
	toolDefinitions, err = aica.NewToolDefinitionsRepository(u.db).AllAvailables()
	if err != nil {
		return err
	}

	for _, toolDefinition := range toolDefinitions {
		var parameters map[string]any

		err := json.Unmarshal(toolDefinition.Parameters, &parameters)
		if err != nil {
			logger.Error("Failed to unmarshal parameters", "error", err)
			return err
		}

		if parameters["required"] == nil {
			parameters["required"] = []string{"SessionID", "RequestID"}
		} else {
			parameters["required"] = append(parameters["required"].([]any), "SessionID", "RequestID")
		}

		if parameters["properties"] == nil {
			parameters["properties"] = map[string]any{
				"SessionID": map[string]any{
					"type":        "string",
					"description": "セッションID",
				},
				"RequestID": map[string]any{
					"type":        "string",
					"description": "リクエストID。なるべく重複しないよう任意のuuidを生成して、リクエストごとにユニークな値を設定してください",
				},
			}
		} else {
			parameters["properties"].(map[string]any)["SessionID"] = map[string]any{
				"type":        "string",
				"description": "セッションID",
			}
			parameters["properties"].(map[string]any)["RequestID"] = map[string]any{
				"type":        "string",
				"description": "リクエストID。なるべく重複しないよう任意のuuidを生成して、リクエストごとにユニークな値を設定してください",
			}
		}

		toolDefinition.Parameters, err = json.Marshal(parameters)
		if err != nil {
			logger.Error("Failed to marshal parameters", "error", err)
			return err
		}

		toolSchema := mcp.NewToolWithRawSchema(
			toolDefinition.Name,
			toolDefinition.Description,
			json.RawMessage(toolDefinition.Parameters))

		createToolHandlerFunc, ok := tools.ToolHanders[toolDefinition.Name]
		if !ok {
			logger.Error("Tool handler not found", "tool_name", toolDefinition.Name)
			continue
		}
		toolHandler := createToolHandlerFunc(u.apiServer, GetProvider)

		mcpServer.AddTool(
			toolSchema,
			toolHandler,
		)
	}

	return nil
}
