user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    proxy_read_timeout 120;
    proxy_send_timeout 120;
    large_client_header_buffers 4 32k;

    log_format  main  '$host $remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" '
                      '"$request_time" "$upstream_response_time" '
                      '"$upstream_addr"';

    access_log  /var/log/nginx/access.log  main;

    sendfile   on;
    # SSEのリアルタイム性を保つため無効化
    tcp_nopush off;

    keepalive_timeout  65;

    #ELB
    set_real_ip_from **********/12;
    real_ip_header  X-Forwarded-For;

    # SSEのリアルタイム性を保つため無効化
    #gzip  on;
    #gzip_types text/css text/javascript application/javascript image/svg+xml;
    expires -1;
    open_file_cache max=100 inactive=10s;

    server_tokens off;

    include /etc/nginx/conf.d/*.conf;
}
