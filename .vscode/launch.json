{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "[MCP]Remote Debug",
      "type": "go",
      "request": "attach",
      "mode": "remote",
      "port": 6000,
      "host": "127.0.0.1",
      "preLaunchTask": "start-debug-server",
      "substitutePath": [
        {
          "to": "/go/src/aica",
          "from": "${workspaceFolder}/src"
        }
      ]
    },
    {
      "name": "[MCP]Local Debug",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/main.go",
      "args": ["--debug", "true"],
      "envFile": "${workspaceFolder}/.env.local"
    }
  ]
}
