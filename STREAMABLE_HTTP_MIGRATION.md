# MCP Server Migration: SSE to StreamableHTTP

## Overview

This document describes the migration of the MCP server from Server-Sent Events (SSE) to StreamableHTTP transport without persistent connections.

## Changes Made

### 1. Session Manager Implementation

Created a new session manager (`src/usecase/aica/session_manager.go`) that handles:
- Session ID generation using UUID
- Session validation with TTL (Time To Live)
- Session termination
- Automatic cleanup of expired sessions
- Session statistics

**Key Features:**
- 30-minute session TTL by default
- Automatic cleanup every 5 minutes
- Thread-safe operations using mutex
- No persistent connections required

### 2. Server Transport Update

Modified `src/usecase/aica/server.go` to:
- Replace `server.NewSSEServer` with `server.NewStreamableHTTPServer`
- Configure StreamableHTTP with session management
- Disable heartbeat for non-persistent connections
- Add graceful shutdown functionality
- Add session statistics endpoint

**Configuration:**
```go
httpServer = server.NewStreamableHTTPServer(
    mcpServer,
    server.WithSessionIdManager(sessionManager),
    server.WithEndpointPath("/mcp"),
    server.WithHeartbeatInterval(0), // Disable heartbeat
)
```

### 3. New Methods Added

- `Shutdown(ctx context.Context) error` - Graceful server shutdown
- `GetSessionStats() map[string]any` - Session statistics

### 4. Testing

Added comprehensive tests (`src/usecase/aica/server_test.go`) covering:
- Session manager functionality
- Server statistics
- Graceful shutdown
- Error handling

## Benefits

1. **No Persistent Connections**: StreamableHTTP doesn't require persistent connections like SSE
2. **Better Resource Management**: Sessions are automatically cleaned up after TTL expires
3. **Improved Scalability**: No need to maintain long-lived connections
4. **Standard HTTP**: Uses standard HTTP requests/responses instead of SSE streams
5. **Session Management**: Built-in session tracking and management

## API Changes

### Endpoint
- Server now listens on `/mcp` endpoint (configurable)
- Uses standard HTTP GET/POST requests instead of SSE streams

### Session Handling
- Sessions are created on-demand
- Session IDs are returned in HTTP headers
- Sessions expire after 30 minutes of inactivity
- No persistent connection required

## Backward Compatibility

This change is **not backward compatible** with SSE clients. Clients need to be updated to use StreamableHTTP transport instead of SSE.

## Configuration

The server can be configured with:
- Session TTL (default: 30 minutes)
- Endpoint path (default: `/mcp`)
- Heartbeat interval (disabled by default)
- Custom session ID manager

## Monitoring

Use `GetSessionStats()` to monitor:
- Number of active sessions
- Server type (StreamableHTTP)
- Persistent connection status (false)

## Testing

Run tests with:
```bash
cd src && go test ./usecase/aica -v
```

All tests should pass, confirming the migration is successful.
