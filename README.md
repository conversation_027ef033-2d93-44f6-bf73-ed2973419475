# 概要

AICAプロジェクトのMCPサーバー

## Model Context Protocol（MCP）とは
https://modelcontextprotocol.io/introduction 参照

## TODO

ツール変更通知はまだできてない。
ここで使っている[mcp-go](https://github.com/mark3labs/mcp-go)も、公式のTypescriptなどのSDKも、全クライアントへの通知機能がないです。
１つのサーバなら、全クライアントへの通知機能をSDKに追加するのは可能だが、サーバ複数がある場合、修正が多分結構大きくなります。
なので、たぶん、MCP利用するバックエンドAPIのほうは、定期プロンプト、ツールを更新する機能を追加するのはやりやすいかも

# ローカルでの起動

## 事前準備

### DB構築

[aica_db_migrationsリポジトリ](https://github.com/MIIDAS-Company/aica_db_migrations)のREADMEを参照

### 環境変数

`.env.example`を`.env.local`にコピーしてください。

## 起動コマンド

`./start_server.sh`

## 検証方法

### 利用ツール

[MCP Inspector](https://github.com/modelcontextprotocol/inspector)

### 起動コマンド

`./start_inspector.sh`

### 検証方法

http://localhost:6274
にアクセスして、「Transport Type」に「SSE」を選んで、「URL」に
http://localhost:8080/sse
を入力して、`Connect`ボタンを押して、`Connected`と表示されたら、接続成功となります。

その後、`Tools`タブでツールの確認ができます。

# 開発者向け

## 開発言語とバージョン

Golang 1.24.5

### 備考

基本最新版にしますので、更新があるときにバージョンアップする予定です。

## プロジェクト構造

### MCPサーバー

#### 入口

`${workspaceFolder}/src/main.go`

#### ツール実装

`src/sdk/tools`

## デバッグ

### MCPサーバー

#### コンテナで起動する場合

コンテナで起動されるサービスをデバッグする方法なので、ローカルでのGolangインストールは不要です。

VSCodeで`launch.json`の`[MCP]Remote Debug`を実行すればコンテナでMCPサーバーを起動し、デバッグできます。

#### 備考

デバッグにはポート6000を利用していますので、`lsof -i:6000`で他にポート6000を利用しているサービスがないかを確認してください。

もしあったら、そのポートを解放するか、下記ファイルのデバッグポートを変えてください。
- .vscode/launch.json
- docker/run_debug.sh
- docker/compose-mcp.yaml

#### VSCodeで起動する場合

##### 準備

Golangのインストールが必要。

`brew install go`よりインストールできます。

##### 起動方法

VSCodeでMCPサーバーを起動して、デバッグする方法です。

VSCodeで`launch.json`の`[MCP]Local Debug`を実行すればMCPサーバーを起動し、デバッグできます。
